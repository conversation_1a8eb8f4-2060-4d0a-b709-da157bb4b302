import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Tabs,
  Tab,
  Container,
  InputAdornment,
  IconButton,
  Fade,
  Slide,
  CircularProgress
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  AdminPanelSettings,
  School,
  Login as LoginIcon,
  AutoAwesome,
  Psychology,
  EmojiObjects
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

const Login = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  
  // Admin login form
  const [adminForm, setAdminForm] = useState({
    email: '',
    password: ''
  });
  
  // Student login form
  const [studentForm, setStudentForm] = useState({
    code: ''
  });

  const { loginAdmin, loginStudent } = useAuth();

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleAdminSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    await loginAdmin(adminForm.email, adminForm.password);
    
    setLoading(false);
  };

  const handleStudentSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    await loginStudent(studentForm.code);
    
    setLoading(false);
  };

  const handleAdminChange = (field) => (e) => {
    setAdminForm(prev => ({
      ...prev,
      [field]: e.target.value
    }));
  };

  const handleStudentChange = (e) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setStudentForm({ code: value });
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ffffff 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: { xs: 1, sm: 2, md: 3 },
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at 20% 80%, rgba(0,0,0,0.02) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(0,0,0,0.02) 0%, transparent 50%)',
          pointerEvents: 'none'
        }
      }}
    >
      <Container
        maxWidth="sm"
        sx={{
          width: '100%',
          maxWidth: { xs: '98%', sm: '520px', md: '650px' },
          mx: 'auto'
        }}
      >
        <Fade in timeout={1000}>
          <Card
            sx={{
              borderRadius: { xs: 2, sm: 3, md: 4 },
              boxShadow: {
                xs: '0 15px 30px rgba(0,0,0,0.1), 0 0 0 1px rgba(0,0,0,0.05)',
                sm: '0 20px 40px rgba(0,0,0,0.12), 0 0 0 1px rgba(0,0,0,0.05)',
                md: '0 25px 50px rgba(0,0,0,0.15), 0 0 0 1px rgba(0,0,0,0.05)'
              },
              overflow: 'hidden',
              background: 'linear-gradient(145deg, #000000 0%, #1a1a1a 100%)',
              border: '1px solid rgba(0,0,0,0.1)',
              position: 'relative',
              zIndex: 1,
              width: '100%',
              maxWidth: '100%'
            }}
          >
            {/* Header */}
            <Box
              sx={{
                background: 'linear-gradient(135deg, #000000 0%, #2a2a2a 50%, #000000 100%)',
                color: 'white',
                textAlign: 'center',
                py: { xs: 2, sm: 3, md: 4 },
                px: { xs: 2, sm: 3, md: 4 },
                position: 'relative',
                borderBottom: '1px solid rgba(255,255,255,0.1)',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.05) 50%, transparent 70%)',
                  pointerEvents: 'none'
                }
              }}
            >
              {/* Logo and Title Container */}
              <Box sx={{
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                alignItems: 'center',
                justifyContent: 'center',
                mb: { xs: 1, sm: 2 },
                gap: { xs: 1, sm: 2 },
                width: '100%',
                px: { xs: 1, sm: 2 }
              }}>
                {/* Academy Icon */}
                <Box sx={{
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <AutoAwesome sx={{
                    fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                    color: '#FFD700',
                    filter: 'drop-shadow(0 0 8px rgba(255, 215, 0, 0.5))',
                    animation: 'sparkle 2s ease-in-out infinite alternate'
                  }} />
                  <EmojiObjects sx={{
                    fontSize: { xs: '1.3rem', sm: '1.7rem', md: '2rem' },
                    color: '#FFA500',
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    filter: 'drop-shadow(0 0 4px rgba(255, 165, 0, 0.3))'
                  }} />
                </Box>

                {/* Academy Title */}
                <Box sx={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'center',
                  overflow: 'visible'
                }}>
                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 700,
                      background: 'linear-gradient(45deg, #FFD700 30%, #FFA500 70%, #ffffff 90%)',
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                      letterSpacing: { xs: '0.5px', sm: '1px', md: '1.5px' },
                      fontFamily: '"Roboto", "Arial", sans-serif',
                      fontSize: { xs: '1.1rem', sm: '1.5rem', md: '1.9rem' },
                      textAlign: 'center',
                      lineHeight: 1.2,
                      whiteSpace: 'nowrap',
                      display: 'inline-block',
                      minWidth: 'max-content'
                    }}
                  >
                    SKILLS WORLD ACADEMY
                  </Typography>
                </Box>
              </Box>

              {/* Instructor Name */}
              <Box sx={{
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                mb: { xs: 0.5, sm: 1 }
              }}>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 500,
                    background: 'linear-gradient(45deg, #FFD700 30%, #FFA500 70%)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    letterSpacing: { xs: '1px', sm: '1.2px', md: '1.5px' },
                    fontFamily: '"Roboto", "Arial", sans-serif',
                    textShadow: '0 1px 2px rgba(0,0,0,0.2)',
                    fontSize: { xs: '1rem', sm: '1.2rem', md: '1.4rem' },
                    textAlign: 'center',
                    whiteSpace: 'nowrap',
                    display: 'inline-block',
                    minWidth: 'max-content'
                  }}
                >
                  ALAA ABD HAMIED
                </Typography>
              </Box>

              <Typography
                variant="h6"
                sx={{
                  opacity: 0.8,
                  color: '#e0e0e0',
                  fontWeight: 300,
                  letterSpacing: { xs: '0.5px', sm: '0.8px', md: '1px' },
                  fontSize: { xs: '0.85rem', sm: '0.95rem', md: '1rem' },
                  textAlign: 'center',
                  px: { xs: 1, sm: 2 },
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}
              >
                🌟 منصة التعلم والتطوير المهني 🌟
              </Typography>

              {/* CSS Animation for sparkle effect */}
              <style>
                {`
                  @keyframes sparkle {
                    0% { transform: rotate(0deg) scale(1); }
                    50% { transform: rotate(180deg) scale(1.1); }
                    100% { transform: rotate(360deg) scale(1); }
                  }
                `}
              </style>
            </Box>

            <CardContent sx={{ p: 0, background: '#000000' }}>
              {/* Tabs */}
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                variant="fullWidth"
                sx={{
                  borderBottom: '1px solid rgba(0,0,0,0.1)',
                  background: 'linear-gradient(90deg, #1a1a1a 0%, #000000 50%, #1a1a1a 100%)',
                  '& .MuiTab-root': {
                    py: 2,
                    fontSize: '1rem',
                    fontWeight: 600,
                    color: 'rgba(255,255,255,0.7)',
                    textTransform: 'none',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      color: 'white',
                      background: 'rgba(255,255,255,0.05)'
                    },
                    '&.Mui-selected': {
                      color: 'white',
                      background: 'linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%)',
                      borderBottom: '2px solid white'
                    }
                  },
                  '& .MuiTabs-indicator': {
                    backgroundColor: '#FFD700',
                    height: '3px',
                    boxShadow: '0 0 8px rgba(255, 215, 0, 0.5)'
                  }
                }}
              >
                <Tab
                  icon={<AdminPanelSettings sx={{
                    color: tabValue === 0 ? '#FFD700' : 'rgba(255,255,255,0.7)',
                    filter: tabValue === 0 ? 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.3))' : 'none'
                  }} />}
                  label="المدير"
                  iconPosition="start"
                />
                <Tab
                  icon={<School sx={{
                    color: tabValue === 1 ? '#FFD700' : 'rgba(255,255,255,0.7)',
                    filter: tabValue === 1 ? 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.3))' : 'none'
                  }} />}
                  label="الطالب"
                  iconPosition="start"
                />
              </Tabs>

              <Box sx={{
                p: { xs: 2, sm: 3, md: 4 },
                background: 'linear-gradient(145deg, #000000 0%, #1a1a1a 100%)',
                minHeight: { xs: '350px', sm: '380px', md: '400px' }
              }}>
                {/* Admin Login */}
                {tabValue === 0 && (
                  <Slide direction="right" in={tabValue === 0} timeout={300}>
                    <Box component="form" onSubmit={handleAdminSubmit}>
                      <Typography
                        variant="h6"
                        sx={{
                          mb: { xs: 2, sm: 3 },
                          textAlign: 'center',
                          color: 'white',
                          fontWeight: 300,
                          letterSpacing: { xs: '0.5px', sm: '1px' },
                          fontSize: { xs: '1.1rem', sm: '1.25rem' }
                        }}
                      >
                        تسجيل دخول المدير
                      </Typography>
                      
                      <TextField
                        fullWidth
                        label="البريد الإلكتروني"
                        type="email"
                        value={adminForm.email}
                        onChange={handleAdminChange('email')}
                        required
                        sx={{
                          mb: { xs: 2, sm: 3 },
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'rgba(255,255,255,0.05)',
                            border: '1px solid rgba(255,255,255,0.2)',
                            borderRadius: { xs: '8px', sm: '10px', md: '12px' },
                            color: 'white',
                            fontSize: { xs: '0.9rem', sm: '1rem' },
                            '&:hover': {
                              border: '1px solid rgba(255,255,255,0.4)',
                              backgroundColor: 'rgba(255,255,255,0.08)'
                            },
                            '&.Mui-focused': {
                              border: '1px solid white',
                              backgroundColor: 'rgba(255,255,255,0.1)',
                              boxShadow: '0 0 0 2px rgba(255,255,255,0.1)'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            color: 'rgba(255,255,255,0.7)',
                            fontSize: { xs: '0.9rem', sm: '1rem' },
                            '&.Mui-focused': {
                              color: 'white'
                            }
                          },
                          '& .MuiOutlinedInput-notchedOutline': {
                            border: 'none'
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <AdminPanelSettings sx={{
                                color: '#FFD700',
                                filter: 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.3))'
                              }} />
                            </InputAdornment>
                          )
                        }}
                      />
                      
                      <TextField
                        fullWidth
                        label="كلمة المرور"
                        type={showPassword ? 'text' : 'password'}
                        value={adminForm.password}
                        onChange={handleAdminChange('password')}
                        required
                        sx={{
                          mb: 4,
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'rgba(255,255,255,0.05)',
                            border: '1px solid rgba(255,255,255,0.2)',
                            borderRadius: '12px',
                            color: 'white',
                            '&:hover': {
                              border: '1px solid rgba(255,255,255,0.4)',
                              backgroundColor: 'rgba(255,255,255,0.08)'
                            },
                            '&.Mui-focused': {
                              border: '1px solid white',
                              backgroundColor: 'rgba(255,255,255,0.1)',
                              boxShadow: '0 0 0 2px rgba(255,255,255,0.1)'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            color: 'rgba(255,255,255,0.7)',
                            '&.Mui-focused': {
                              color: 'white'
                            }
                          },
                          '& .MuiOutlinedInput-notchedOutline': {
                            border: 'none'
                          }
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => setShowPassword(!showPassword)}
                                edge="end"
                                sx={{
                                  color: '#FFD700',
                                  '&:hover': {
                                    color: '#FFA500',
                                    background: 'rgba(255, 215, 0, 0.1)'
                                  }
                                }}
                              >
                                {showPassword ? <VisibilityOff /> : <Visibility />}
                              </IconButton>
                            </InputAdornment>
                          )
                        }}
                      />
                      
                      <Button
                        type="submit"
                        fullWidth
                        variant="contained"
                        size="large"
                        disabled={loading}
                        startIcon={loading ? <CircularProgress size={20} sx={{ color: '#FFD700' }} /> : <LoginIcon sx={{ color: '#FFD700' }} />}
                        sx={{
                          py: { xs: 1.2, sm: 1.5 },
                          fontSize: { xs: '1rem', sm: '1.1rem' },
                          fontWeight: 600,
                          background: 'linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%)',
                          color: '#000000',
                          border: '1px solid rgba(255,255,255,0.2)',
                          borderRadius: { xs: '8px', sm: '10px', md: '12px' },
                          textTransform: 'none',
                          boxShadow: '0 4px 15px rgba(255,255,255,0.2)',
                          transition: 'all 0.3s ease',
                          minHeight: { xs: '48px', sm: '52px' },
                          '&:hover': {
                            background: 'linear-gradient(135deg, #f5f5f5 0%, #ffffff 100%)',
                            transform: 'translateY(-2px)',
                            boxShadow: '0 6px 20px rgba(255,255,255,0.3)'
                          },
                          '&:disabled': {
                            background: 'rgba(255,255,255,0.3)',
                            color: 'rgba(0,0,0,0.5)'
                          }
                        }}
                      >
                        {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
                      </Button>
                    </Box>
                  </Slide>
                )}

                {/* Student Login */}
                {tabValue === 1 && (
                  <Slide direction="left" in={tabValue === 1} timeout={300}>
                    <Box component="form" onSubmit={handleStudentSubmit}>
                      <Typography
                        variant="h6"
                        sx={{
                          mb: { xs: 2, sm: 3 },
                          textAlign: 'center',
                          color: 'white',
                          fontWeight: 300,
                          letterSpacing: { xs: '0.5px', sm: '1px' },
                          fontSize: { xs: '1.1rem', sm: '1.25rem' }
                        }}
                      >
                        تسجيل دخول الطالب
                      </Typography>

                      <Typography
                        variant="body2"
                        sx={{
                          mb: { xs: 2, sm: 3 },
                          textAlign: 'center',
                          color: 'rgba(255,255,255,0.7)',
                          fontSize: { xs: '0.85rem', sm: '0.95rem' },
                          px: { xs: 1, sm: 0 }
                        }}
                      >
                        أدخل الكود المكون من 6 أرقام الذي حصلت عليه من المدير
                      </Typography>
                      
                      <TextField
                        fullWidth
                        label="كود الطالب (6 أرقام)"
                        value={studentForm.code}
                        onChange={handleStudentChange}
                        required
                        inputProps={{
                          maxLength: 6,
                          style: {
                            textAlign: 'center',
                            fontSize: window.innerWidth < 600 ? '1.2rem' : '1.5rem',
                            letterSpacing: window.innerWidth < 600 ? '0.3rem' : '0.5rem',
                            color: 'white'
                          }
                        }}
                        sx={{
                          mb: { xs: 3, sm: 4 },
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'rgba(255,255,255,0.05)',
                            border: '1px solid rgba(255,255,255,0.2)',
                            borderRadius: { xs: '8px', sm: '10px', md: '12px' },
                            color: 'white',
                            minHeight: { xs: '56px', sm: '64px' },
                            '&:hover': {
                              border: '1px solid rgba(255,255,255,0.4)',
                              backgroundColor: 'rgba(255,255,255,0.08)'
                            },
                            '&.Mui-focused': {
                              border: '1px solid white',
                              backgroundColor: 'rgba(255,255,255,0.1)',
                              boxShadow: '0 0 0 2px rgba(255,255,255,0.1)'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            color: 'rgba(255,255,255,0.7)',
                            fontSize: { xs: '0.9rem', sm: '1rem' },
                            '&.Mui-focused': {
                              color: 'white'
                            }
                          },
                          '& .MuiOutlinedInput-notchedOutline': {
                            border: 'none'
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <School sx={{
                                color: '#FFD700',
                                filter: 'drop-shadow(0 0 4px rgba(255, 215, 0, 0.3))'
                              }} />
                            </InputAdornment>
                          )
                        }}
                      />
                      
                      <Button
                        type="submit"
                        fullWidth
                        variant="contained"
                        size="large"
                        disabled={loading || studentForm.code.length !== 6}
                        startIcon={loading ? <CircularProgress size={20} sx={{ color: '#FFD700' }} /> : <LoginIcon sx={{ color: '#FFD700' }} />}
                        sx={{
                          py: 1.5,
                          fontSize: '1.1rem',
                          fontWeight: 600,
                          background: 'linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%)',
                          color: '#000000',
                          border: '1px solid rgba(255,255,255,0.2)',
                          borderRadius: '12px',
                          textTransform: 'none',
                          boxShadow: '0 4px 15px rgba(255,255,255,0.2)',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            background: 'linear-gradient(135deg, #f5f5f5 0%, #ffffff 100%)',
                            transform: 'translateY(-2px)',
                            boxShadow: '0 6px 20px rgba(255,255,255,0.3)'
                          },
                          '&:disabled': {
                            background: 'rgba(255,255,255,0.3)',
                            color: 'rgba(0,0,0,0.5)'
                          }
                        }}
                      >
                        {loading ? 'جاري تسجيل الدخول...' : 'دخول'}
                      </Button>
                    </Box>
                  </Slide>
                )}
              </Box>
            </CardContent>
          </Card>
        </Fade>
      </Container>
    </Box>
  );
};

export default Login;
