import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Tabs,
  Tab,
  Container,
  InputAdornment,
  IconButton,
  Fade,
  Slide,
  CircularProgress
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  AdminPanelSettings,
  School,
  Login as LoginIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

const Login = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  
  // Admin login form
  const [adminForm, setAdminForm] = useState({
    email: '',
    password: ''
  });
  
  // Student login form
  const [studentForm, setStudentForm] = useState({
    code: ''
  });

  const { loginAdmin, loginStudent } = useAuth();

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleAdminSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    await loginAdmin(adminForm.email, adminForm.password);
    
    setLoading(false);
  };

  const handleStudentSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    await loginStudent(studentForm.code);
    
    setLoading(false);
  };

  const handleAdminChange = (field) => (e) => {
    setAdminForm(prev => ({
      ...prev,
      [field]: e.target.value
    }));
  };

  const handleStudentChange = (e) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setStudentForm({ code: value });
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 2,
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%)',
          pointerEvents: 'none'
        }
      }}
    >
      <Container maxWidth="sm">
        <Fade in timeout={1000}>
          <Card
            sx={{
              borderRadius: 4,
              boxShadow: '0 25px 50px rgba(0,0,0,0.8), 0 0 0 1px rgba(255,255,255,0.1)',
              overflow: 'hidden',
              background: 'linear-gradient(145deg, #1a1a1a 0%, #000000 100%)',
              border: '1px solid rgba(255,255,255,0.1)',
              position: 'relative',
              zIndex: 1
            }}
          >
            {/* Header */}
            <Box
              sx={{
                background: 'linear-gradient(135deg, #000000 0%, #2a2a2a 50%, #000000 100%)',
                color: 'white',
                textAlign: 'center',
                py: 4,
                px: 3,
                position: 'relative',
                borderBottom: '1px solid rgba(255,255,255,0.1)',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.05) 50%, transparent 70%)',
                  pointerEvents: 'none'
                }
              }}
            >
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 700,
                  mb: 1,
                  background: 'linear-gradient(45deg, #ffffff 30%, #e0e0e0 90%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  textShadow: '0 2px 4px rgba(0,0,0,0.3)'
                }}
              >
                علاء عبد الحميد
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  opacity: 0.8,
                  color: '#e0e0e0',
                  fontWeight: 300,
                  letterSpacing: '0.5px'
                }}
              >
                منصة كورسات التسويق
              </Typography>
            </Box>

            <CardContent sx={{ p: 0, background: '#000000' }}>
              {/* Tabs */}
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                variant="fullWidth"
                sx={{
                  borderBottom: '1px solid rgba(255,255,255,0.1)',
                  background: 'linear-gradient(90deg, #1a1a1a 0%, #000000 50%, #1a1a1a 100%)',
                  '& .MuiTab-root': {
                    py: 2,
                    fontSize: '1rem',
                    fontWeight: 600,
                    color: 'rgba(255,255,255,0.7)',
                    textTransform: 'none',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      color: 'white',
                      background: 'rgba(255,255,255,0.05)'
                    },
                    '&.Mui-selected': {
                      color: 'white',
                      background: 'linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%)',
                      borderBottom: '2px solid white'
                    }
                  },
                  '& .MuiTabs-indicator': {
                    backgroundColor: 'white',
                    height: '2px'
                  }
                }}
              >
                <Tab
                  icon={<AdminPanelSettings />}
                  label="المدير"
                  iconPosition="start"
                />
                <Tab
                  icon={<School />}
                  label="الطالب"
                  iconPosition="start"
                />
              </Tabs>

              <Box sx={{
                p: 4,
                background: 'linear-gradient(145deg, #000000 0%, #1a1a1a 100%)',
                minHeight: '400px'
              }}>
                {/* Admin Login */}
                {tabValue === 0 && (
                  <Slide direction="right" in={tabValue === 0} timeout={300}>
                    <Box component="form" onSubmit={handleAdminSubmit}>
                      <Typography
                        variant="h6"
                        sx={{
                          mb: 3,
                          textAlign: 'center',
                          color: 'white',
                          fontWeight: 300,
                          letterSpacing: '1px'
                        }}
                      >
                        تسجيل دخول المدير
                      </Typography>
                      
                      <TextField
                        fullWidth
                        label="البريد الإلكتروني"
                        type="email"
                        value={adminForm.email}
                        onChange={handleAdminChange('email')}
                        required
                        sx={{
                          mb: 3,
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'rgba(255,255,255,0.05)',
                            border: '1px solid rgba(255,255,255,0.2)',
                            borderRadius: '12px',
                            color: 'white',
                            '&:hover': {
                              border: '1px solid rgba(255,255,255,0.4)',
                              backgroundColor: 'rgba(255,255,255,0.08)'
                            },
                            '&.Mui-focused': {
                              border: '1px solid white',
                              backgroundColor: 'rgba(255,255,255,0.1)',
                              boxShadow: '0 0 0 2px rgba(255,255,255,0.1)'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            color: 'rgba(255,255,255,0.7)',
                            '&.Mui-focused': {
                              color: 'white'
                            }
                          },
                          '& .MuiOutlinedInput-notchedOutline': {
                            border: 'none'
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <AdminPanelSettings sx={{ color: 'rgba(255,255,255,0.7)' }} />
                            </InputAdornment>
                          )
                        }}
                      />
                      
                      <TextField
                        fullWidth
                        label="كلمة المرور"
                        type={showPassword ? 'text' : 'password'}
                        value={adminForm.password}
                        onChange={handleAdminChange('password')}
                        required
                        sx={{
                          mb: 4,
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'rgba(255,255,255,0.05)',
                            border: '1px solid rgba(255,255,255,0.2)',
                            borderRadius: '12px',
                            color: 'white',
                            '&:hover': {
                              border: '1px solid rgba(255,255,255,0.4)',
                              backgroundColor: 'rgba(255,255,255,0.08)'
                            },
                            '&.Mui-focused': {
                              border: '1px solid white',
                              backgroundColor: 'rgba(255,255,255,0.1)',
                              boxShadow: '0 0 0 2px rgba(255,255,255,0.1)'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            color: 'rgba(255,255,255,0.7)',
                            '&.Mui-focused': {
                              color: 'white'
                            }
                          },
                          '& .MuiOutlinedInput-notchedOutline': {
                            border: 'none'
                          }
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => setShowPassword(!showPassword)}
                                edge="end"
                                sx={{ color: 'rgba(255,255,255,0.7)' }}
                              >
                                {showPassword ? <VisibilityOff /> : <Visibility />}
                              </IconButton>
                            </InputAdornment>
                          )
                        }}
                      />
                      
                      <Button
                        type="submit"
                        fullWidth
                        variant="contained"
                        size="large"
                        disabled={loading}
                        startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <LoginIcon />}
                        sx={{
                          py: 1.5,
                          fontSize: '1.1rem',
                          fontWeight: 600,
                          background: 'linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%)',
                          color: '#000000',
                          border: '1px solid rgba(255,255,255,0.2)',
                          borderRadius: '12px',
                          textTransform: 'none',
                          boxShadow: '0 4px 15px rgba(255,255,255,0.2)',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            background: 'linear-gradient(135deg, #f5f5f5 0%, #ffffff 100%)',
                            transform: 'translateY(-2px)',
                            boxShadow: '0 6px 20px rgba(255,255,255,0.3)'
                          },
                          '&:disabled': {
                            background: 'rgba(255,255,255,0.3)',
                            color: 'rgba(0,0,0,0.5)'
                          }
                        }}
                      >
                        {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
                      </Button>
                    </Box>
                  </Slide>
                )}

                {/* Student Login */}
                {tabValue === 1 && (
                  <Slide direction="left" in={tabValue === 1} timeout={300}>
                    <Box component="form" onSubmit={handleStudentSubmit}>
                      <Typography
                        variant="h6"
                        sx={{
                          mb: 3,
                          textAlign: 'center',
                          color: 'white',
                          fontWeight: 300,
                          letterSpacing: '1px'
                        }}
                      >
                        تسجيل دخول الطالب
                      </Typography>

                      <Typography
                        variant="body2"
                        sx={{
                          mb: 3,
                          textAlign: 'center',
                          color: 'rgba(255,255,255,0.7)',
                          fontSize: '0.95rem'
                        }}
                      >
                        أدخل الكود المكون من 6 أرقام الذي حصلت عليه من المدير
                      </Typography>
                      
                      <TextField
                        fullWidth
                        label="كود الطالب (6 أرقام)"
                        value={studentForm.code}
                        onChange={handleStudentChange}
                        required
                        inputProps={{
                          maxLength: 6,
                          style: {
                            textAlign: 'center',
                            fontSize: '1.5rem',
                            letterSpacing: '0.5rem',
                            color: 'white'
                          }
                        }}
                        sx={{
                          mb: 4,
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'rgba(255,255,255,0.05)',
                            border: '1px solid rgba(255,255,255,0.2)',
                            borderRadius: '12px',
                            color: 'white',
                            '&:hover': {
                              border: '1px solid rgba(255,255,255,0.4)',
                              backgroundColor: 'rgba(255,255,255,0.08)'
                            },
                            '&.Mui-focused': {
                              border: '1px solid white',
                              backgroundColor: 'rgba(255,255,255,0.1)',
                              boxShadow: '0 0 0 2px rgba(255,255,255,0.1)'
                            }
                          },
                          '& .MuiInputLabel-root': {
                            color: 'rgba(255,255,255,0.7)',
                            '&.Mui-focused': {
                              color: 'white'
                            }
                          },
                          '& .MuiOutlinedInput-notchedOutline': {
                            border: 'none'
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <School sx={{ color: 'rgba(255,255,255,0.7)' }} />
                            </InputAdornment>
                          )
                        }}
                      />
                      
                      <Button
                        type="submit"
                        fullWidth
                        variant="contained"
                        size="large"
                        disabled={loading || studentForm.code.length !== 6}
                        startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <LoginIcon />}
                        sx={{
                          py: 1.5,
                          fontSize: '1.1rem',
                          fontWeight: 600,
                          background: 'linear-gradient(45deg, #4CAF50 30%, #8BC34A 90%)',
                          '&:hover': {
                            background: 'linear-gradient(45deg, #388E3C 30%, #689F38 90%)',
                          }
                        }}
                      >
                        {loading ? 'جاري تسجيل الدخول...' : 'دخول'}
                      </Button>
                    </Box>
                  </Slide>
                )}
              </Box>
            </CardContent>
          </Card>
        </Fade>
      </Container>
    </Box>
  );
};

export default Login;
